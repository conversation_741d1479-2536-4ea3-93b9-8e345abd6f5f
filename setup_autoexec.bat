@echo off
echo ========================================
echo    Auto-Execute Setup Helper
echo ========================================
echo.

echo This script will help you find and copy the gun mod script to your executor's autoexec folder.
echo.

echo Step 1: Finding common executor autoexec folders...
echo.

set "found=false"

REM Check for Synapse X
if exist "%LOCALAPPDATA%\Synapse\autoexec" (
    echo [FOUND] Synapse X autoexec folder: %LOCALAPPDATA%\Synapse\autoexec
    set "synapse_path=%LOCALAPPDATA%\Synapse\autoexec"
    set "found=true"
)

REM Check for KRNL
if exist "%APPDATA%\Krnl\autoexec" (
    echo [FOUND] KRNL autoexec folder: %APPDATA%\Krnl\autoexec
    set "krnl_path=%APPDATA%\Krnl\autoexec"
    set "found=true"
)

REM Check for Fluxus
if exist "%LOCALAPPDATA%\Fluxus\autoexec" (
    echo [FOUND] Fluxus autoexec folder: %LOCALAPPDATA%\Fluxus\autoexec
    set "fluxus_path=%LOCALAPPDATA%\Fluxus\autoexec"
    set "found=true"
)

REM Check for Script-Ware
if exist "%LOCALAPPDATA%\Script-Ware\autoexec" (
    echo [FOUND] Script-Ware autoexec folder: %LOCALAPPDATA%\Script-Ware\autoexec
    set "scriptware_path=%LOCALAPPDATA%\Script-Ware\autoexec"
    set "found=true"
)

echo.

if "%found%"=="false" (
    echo [WARNING] No common executor autoexec folders found automatically.
    echo.
    echo Please manually locate your executor's autoexec folder and copy:
    echo - gun_mods_oneshot.lua
    echo.
    echo Common locations to check:
    echo - Your executor's installation folder
    echo - AppData\Local\[ExecutorName]\autoexec
    echo - AppData\Roaming\[ExecutorName]\autoexec
    pause
    exit
)

echo Step 2: Copying script files...
echo.

REM Copy to found executors
if defined synapse_path (
    copy "gun_mods_oneshot.lua" "%synapse_path%\" >nul 2>&1
    if %errorlevel%==0 (
        echo [SUCCESS] Copied to Synapse X autoexec folder
    ) else (
        echo [ERROR] Failed to copy to Synapse X folder
    )
)

if defined krnl_path (
    copy "gun_mods_oneshot.lua" "%krnl_path%\" >nul 2>&1
    if %errorlevel%==0 (
        echo [SUCCESS] Copied to KRNL autoexec folder
    ) else (
        echo [ERROR] Failed to copy to KRNL folder
    )
)

if defined fluxus_path (
    copy "gun_mods_oneshot.lua" "%fluxus_path%\" >nul 2>&1
    if %errorlevel%==0 (
        echo [SUCCESS] Copied to Fluxus autoexec folder
    ) else (
        echo [ERROR] Failed to copy to Fluxus folder
    )
)

if defined scriptware_path (
    copy "gun_mods_oneshot.lua" "%scriptware_path%\" >nul 2>&1
    if %errorlevel%==0 (
        echo [SUCCESS] Copied to Script-Ware autoexec folder
    ) else (
        echo [ERROR] Failed to copy to Script-Ware folder
    )
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Restart your script executor
echo 2. Join a Roblox game with weapons
echo 3. The script should auto-load and show: "Gun Mods 1-Shot Kill loaded successfully!"
echo.
pause
